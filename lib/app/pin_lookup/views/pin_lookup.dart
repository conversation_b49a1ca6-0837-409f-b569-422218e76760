import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/app_events/cubit/app_event_cubit.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/cubit/authentication_cubit.dart';

import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_languages.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_modes.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/repositories/setting_repository.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/cubit/lookup_cubit.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/report_data.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/views/print_label.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/views/print_label_button.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/remediate_page.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/repositories/scanner_repository.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/i_data_provider_service.dart';
import 'package:sort_pro_printer_flutter/services/device_info/i_device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/i_lookup_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/sound/sound_service.dart';
import 'package:sort_pro_printer_flutter/services/vibration/vibration_service.dart';
import 'package:sort_pro_printer_flutter/styles/scale_size.dart';
import 'package:sort_pro_printer_flutter/utils/pin_validator.dart';
import 'peripherals.dart';

class PinLookupPage extends StatefulWidget {
  static MaterialPageRoute route(
    BuildContext context,
  ) {
    final logService = context.read<ILogService>();
    final lookupService = context.read<ILookupService>();
    final dataProviderService = context.read<IDataProviderService>();
    final deviceInfoService = context.read<IDeviceInfoService>();
    final scannerRepository = context.read<ScannerRepository>();
    final peripheralsCubit = context.read<PeripheralsCubit>();
    final userId = context.read<AuthenticationCubit>().state.user.id;
    final mode = context.read<UserSettingCubit>().state.mode!;
    context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "PinLookupPage"});
    return MaterialPageRoute(
      builder: (_) => BlocProvider.value(
        value: context.read<UserSettingCubit>(),
        child: BlocProvider(
          create: (context) {
            return LookupCubit(
              scannerRepository: scannerRepository,
              settingRepository: SettingRepository(),
              peripheralsCubit: peripheralsCubit,
              appEventCubit: context.read<AppEventCubit>(),
              userSettingCubit: context.read<UserSettingCubit>(),
              appSettingCubit: context.read<AppSettingCubit>(),
              lookupService: lookupService,
              dataProviderService: dataProviderService,
              deviceInfoService: deviceInfoService,
              logService: logService,
              soundService: SoundService(),
              vibrationService: VibrationService(),
              pinValidator: PinValidator(logService: logService),
              userId: userId,
              mode: mode,
            );
          },
          child: const PinLookupPage._(),
        ),
      ),
    );
  }

  const PinLookupPage._({Key? key}) : super(key: key);

  @override
  State<PinLookupPage> createState() => _PinLookupPageState();
}

class _PinLookupPageState extends State<PinLookupPage> {
  late final ScannerRepository scannerRepository;
  StreamSubscription<Barcode>? streamSubscription;
  Barcode scannedBarcode = Barcode.empty;

  @override
  void initState() {
    scannerRepository = context.read<ScannerRepository>();
    subscribeToScannedBarcodes();
    super.initState();
  }

  @override
  void dispose() {
    unsubscribeFromScannedBarcodes();
    super.dispose();
  }

  subscribeToScannedBarcodes() {
    streamSubscription = scannerRepository.scannedBarcodes.listen((barcode) {
      scannedBarcode = barcode;
      context.read<LookupCubit>().lookupPackageInfoFromBarcode(barcode);
    });
  }

  unsubscribeFromScannedBarcodes() {
    streamSubscription?.cancel();
  }

  Future<void> onLookupStateChange(BuildContext context, LookupState state) async {
    if (state is LookupPrintResults && state.printLabel) {
      String? languageCode = context.read<UserSettingCubit>().state.languageCode ?? SupportedLanguages.english;
      context.read<PeripheralsCubit>().printLookupLabel(state.currentResult, languageCode);
    }

    if (state is LookupPrintResults && state.shouldRemediate) {
      final navigator = Navigator.of(context);
      final remediatePageRoute = RemediatePage.route(context, scannedBarcode: scannedBarcode); //
      final lookupCubit = context.read<LookupCubit>();

      LookupResult? remediated;
      // to stop current page from listening to scans and leave that job to the remediate page
      unsubscribeFromScannedBarcodes();
      remediated = await navigator.push<LookupResult?>(remediatePageRoute);
      // re-enable barcode subscription for current (print ode) page
      subscribeToScannedBarcodes();
      // remediated holds the value coming from remediate page, if that's null it means user skipped remediation
      // if remediate is not null, means we expect a RemediateResult to be returned that has a route+shelf
      lookupCubit.confirmPrintForMode3(remediated);
    }
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      onKeyEvent: kDebugMode
          ? (key) {
              if (key is KeyUpEvent) {
                context.read<LookupCubit>().lookupPackageInfoFromBarcode(
                      const Barcode(
                          "V01~A01|D01~T5J3S4|R01~ACME WIDGET SUPPLY|R02~SUITE 200|R03~5995|R04~AVEBURY DRIVE|R05~|R06~MISSISSAUGA|R07~L5R3T8|S01~VVV000000001|S02~VVV000000001|S03~1|S04~00|S05~0|S06~0|S07~0|S08~20140611|S09~2|S10~5|S11~18|S12~99|S13~55|S14~YYZ|S15~00|B01~202|B02~PP",
                          BarcodeSymbology.pdf417),
                    );
              }
            }
          : null,
      focusNode: FocusNode(),
      child: Scaffold(
        appBar: CustomAppBar(
          leading: TextButton(onPressed: () => Navigator.pop(context), child: const Icon(Icons.menu, color: Colors.white)),
          title: SizedBox(
            width: 130,
            child: Image.asset("assets/sortpro_app_logo.png"),
          ),
          initialHelpDocumentPage: context.read<UserSettingCubit>().state.mode == SupportedModes.print.name
              ? HelpDocumentPage.printMode
              : HelpDocumentPage.bothMode,
        ),
        bottomNavigationBar: Container(
          decoration: const BoxDecoration(
            color: Color(0xff212121),
            border: Border(top: BorderSide(color: Color(0xff646464))),
          ),
          child: const Padding(
            padding: EdgeInsets.symmetric(vertical: 25, horizontal: 17),
            child: Row(
              children: [
                Expanded(
                  child: PrintRemLabelButton(),
                ),
                SizedBox(
                  width: 20.0,
                ),
                Peripherals(),
              ],
            ),
          ),
        ),
        body: BlocConsumer<LookupCubit, LookupState>(
          listener: onLookupStateChange,
          builder: (context, state) {
            if (state is ScanError) {
              return Center(child: Text("Error, ${state.errorMessage}"));
            }
            if (state is LookingUpPrintResults) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state is LookupPrintResults && context.read<LookupCubit>().scannedPins.isNotEmpty) {
              return _buildResultsView(state.currentResult, state.pastResult, state.reportData);
            }

            if (state is PinInvalidState) {
              return _buildResultsView(state.currentResult, state.pastResult, state.reportData);
            }


            return Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    AppLocalizations.of(context)!.common_lbl_ready,
                    style: Theme.of(context).textTheme.headlineLarge!.copyWith(color: const Color(0xFF32D74B), fontWeight: FontWeight.w600),
                    textAlign: TextAlign.center,
                    textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
                  ),
                  const SizedBox(height: 22),
                  Text(
                    AppLocalizations.of(context)!.pinLookup_lbl_scanInstructions,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w700),
                    textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _reportDataRow(String label, int value, bool addDivider) {
    return Column(
      children: [
        Row(
          children: [
            Text(label),
            const Spacer(),
            Text(
              value.toString(),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 2),
        addDivider
            ? const Divider(
                height: 0.1,
                color: Color(0xB4B4B4B4),
              )
            : const SizedBox(),
      ],
    );
  }

  _buildResultsView(LookupResult? currentResult, LookupResult? pastResult, ReportData reportData) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 17),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 10),
          Text(
            AppLocalizations.of(context)!.pinLookup_lbl_latestScan,
            style: Theme.of(context).textTheme.titleMedium!.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          currentResult != null
              ? PrintLabel(
                  lookupResult: currentResult,
                  canReprint: true,
                  isPastResult: false,
                )
              : const SizedBox(),
          const SizedBox(
            height: 10,
          ),
          pastResult != null
              ? Text(
                  AppLocalizations.of(context)!.pinLookup_lbl_previousScan,
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(fontWeight: FontWeight.bold),
                )
              : const SizedBox(),
          const SizedBox(height: 5),
          pastResult != null
              ? ListTile(
                  title: PrintLabel(lookupResult: pastResult, isPastResult: true),
                  contentPadding: const EdgeInsets.only(bottom: 5),
                )
              : const SizedBox(),
          const SizedBox(height: 5),
          Text(
            AppLocalizations.of(context)!.printReport_lbl_scanReport,
            style: Theme.of(context).textTheme.titleMedium!.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 5),
          _reportDataRow(AppLocalizations.of(context)!.printReport_lbl_rte, reportData.rteLabels, true),
          _reportDataRow(AppLocalizations.of(context)!.printReport_lbl_rem, reportData.remLabels, true),
          _reportDataRow(AppLocalizations.of(context)!.printReport_lbl_srr, reportData.srrLabels, true),
          _reportDataRow(AppLocalizations.of(context)!.printReport_lbl_other, reportData.other, true),
          _reportDataRow(AppLocalizations.of(context)!.printReport_lbl_total, reportData.parcelsScanned, false),
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}
