import 'package:sort_pro_printer_flutter/app/manage/data/models/terminal.dart';

const backupTerminalList = [
  Terminal(id: "050", name: "<PERSON><PERSON>ph", provinceCode: "ON"),
  Terminal(id: "012", name: "Toronto 2 (Metro West)", provinceCode: "ON"),
  Terminal(id: "560", name: "Scarborough", provinceCode: "ON"),
  Terminal(id: "557", name: "Burlington", provinceCode: "ON"),
  Terminal(id: "552", name: "North York", provinceCode: "ON"),
  Terminal(id: "524", name: "Tees Road", provinceCode: "QC"),
  Terminal(id: "523", name: "Anjou", provinceCode: "QC"),
  Terminal(id: "522", name: "La<PERSON>", provinceCode: "QC"),
  Terminal(id: "521", name: "Boucherville", provinceCode: "QC"),
  Terminal(id: "519", name: "Ottawa", provinceCode: "ON"),
  Terminal(id: "516", name: "Ville St Pierre", provinceCode: "QC"),
  Terminal(id: "511", name: "Mississauga West", provinceCode: "ON"),
  Terminal(id: "492", name: "Mount Hope", provinceCode: "ON"),
  Terminal(id: "491", name: "Vaughan", provinceCode: "ON"),
  Terminal(id: "221", name: "St Jerome", provinceCode: "QC"),
  Terminal(id: "493", name: "Edmonton", provinceCode: "AB"),
  Terminal(id: "490", name: "Mississauga East", provinceCode: "ON"),
  Terminal(id: "512", name: "Pickering", provinceCode: "ON"),
  Terminal(id: "517", name: "Dartmouth", provinceCode: "NS"),
  Terminal(id: "508", name: "Saskatoon", provinceCode: "SK"),
  Terminal(id: "518", name: "Quebec", provinceCode: "QC"),
  Terminal(id: "558", name: "Barrie", provinceCode: "ON"),
  Terminal(id: "503", name: "London", provinceCode: "ON"),
  Terminal(id: "561", name: "NOTL", provinceCode: "ON"),
  Terminal(id: "542", name: "Burnaby", provinceCode: "BC")
];
